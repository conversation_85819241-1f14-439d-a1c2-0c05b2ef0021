"""
Service classes for betting functionality
"""

import uuid
import logging
from decimal import Decimal
from django.utils import timezone
from django.db import transaction
from django.conf import settings

from accounts.models import CustomUser
from sports.models import Odds, Market, Event
from .models import Bet, BetSelection
from .exceptions import (
    InsufficientBalanceException,
    InvalidStakeException,
    InvalidOddsException,
    EventStartedException,
    MaximumStakeExceededException,
    MinimumStakeException,
    MaximumSelectionsExceededException,
    BetPlacementException,
    DuplicateSelectionException,
    IncompatibleSelectionsException,
    BetNotFoundException,
    BetAlreadySettledException,
    MarketSuspendedException,
    BetCancellationException
)

logger = logging.getLogger(__name__)

# Constants
MIN_STAKE = Decimal('1.00')  # Minimum stake amount
MAX_STAKE_SINGLE = Decimal('100000.00')  # Maximum stake for single bets
MAX_STAKE_MULTI = Decimal('50000.00')  # Maximum stake for multi bets
MAX_SELECTIONS = 20  # Maximum number of selections in a multi-bet
MAX_POTENTIAL_WINNINGS = Decimal('1000000.00')  # Maximum potential winnings


class BetValidator:
    """
    Service class for validating bets before placement
    """
    
    @staticmethod
    def validate_stake(stake, bet_type='single'):
        """
        Validate stake amount
        
        Args:
            stake (Decimal): Stake amount
            bet_type (str): Type of bet ('single', 'multi', etc.)
            
        Raises:
            InvalidStakeException: If stake is invalid
            MinimumStakeException: If stake is below minimum
            MaximumStakeExceededException: If stake exceeds maximum
        """
        try:
            stake = Decimal(str(stake))
        except (ValueError, TypeError, InvalidOperation):
            raise InvalidStakeException("Stake must be a valid number")
        
        if stake < MIN_STAKE:
            raise MinimumStakeException(f"Minimum stake is {MIN_STAKE}")
        
        max_stake = MAX_STAKE_SINGLE if bet_type == 'single' else MAX_STAKE_MULTI
        
        if stake > max_stake:
            raise MaximumStakeExceededException(f"Maximum stake for {bet_type} bet is {max_stake}")
        
        return True
    
    @staticmethod
    def validate_user_can_bet(user):
        """
        Validate that user can place bets
        
        Args:
            user (CustomUser): User placing the bet
            
        Raises:
            BetPlacementException: If user cannot place bets
        """
        if not user.can_bet():
            reasons = []
            if not user.is_verified:
                reasons.append("account is not verified")
            if not user.is_adult:
                reasons.append("age verification required")
            if user.is_suspended:
                reasons.append("account is suspended")
            if not user.is_active:
                reasons.append("account is inactive")
            
            reason_text = " and ".join(reasons)
            raise BetPlacementException(f"Cannot place bet because {reason_text}")
        
        return True
    
    @staticmethod
    def validate_balance(user, stake):
        """
        Validate user has sufficient balance
        
        Args:
            user (CustomUser): User placing the bet
            stake (Decimal): Stake amount
            
        Raises:
            InsufficientBalanceException: If balance is insufficient
        """
        if user.balance < Decimal(str(stake)):
            raise InsufficientBalanceException(
                f"Insufficient balance. Current balance: {user.balance}, Required: {stake}"
            )
        
        return True
    
    @staticmethod
    def validate_odds(odds_id, expected_value=None):
        """
        Validate odds exist and match expected value
        
        Args:
            odds_id (int): ID of odds to validate
            expected_value (Decimal, optional): Expected odds value
            
        Raises:
            InvalidOddsException: If odds are invalid or have changed
        """
        try:
            odds = Odds.objects.get(pk=odds_id)
        except Odds.DoesNotExist:
            raise InvalidOddsException("Selected odds no longer exist")
        
        if not odds.is_active:
            raise MarketSuspendedException("This market is currently suspended")
        
        if expected_value and odds.odds_value != Decimal(str(expected_value)):
            raise InvalidOddsException(
                f"Odds have changed from {expected_value} to {odds.odds_value}"
            )
        
        return odds
    
    @staticmethod
    def validate_event_not_started(event):
        """
        Validate event has not started
        
        Args:
            event (Event): Event to validate
            
        Raises:
            EventStartedException: If event has already started
        """
        if event.start_time <= timezone.now():
            raise EventStartedException(f"Event '{event}' has already started")
        
        return True
    
    @staticmethod
    def validate_selections_count(count):
        """
        Validate number of selections
        
        Args:
            count (int): Number of selections
            
        Raises:
            MaximumSelectionsExceededException: If too many selections
        """
        if count > MAX_SELECTIONS:
            raise MaximumSelectionsExceededException(f"Maximum {MAX_SELECTIONS} selections allowed")
        
        return True
    
    @staticmethod
    def validate_no_duplicate_selections(selections):
        """
        Validate no duplicate selections
        
        Args:
            selections (list): List of selection data
            
        Raises:
            DuplicateSelectionException: If duplicate selections found
        """
        odds_ids = [s.get('odds_id') for s in selections]
        if len(odds_ids) != len(set(odds_ids)):
            raise DuplicateSelectionException("Duplicate selections are not allowed")
        
        return True
    
    @staticmethod
    def validate_compatible_selections(selections, bet_type):
        """
        Validate selections are compatible
        
        Args:
            selections (list): List of selection data
            bet_type (str): Type of bet
            
        Raises:
            IncompatibleSelectionsException: If selections are incompatible
        """
        if bet_type == 'single':
            return True
        
        # For multi-bets, check that selections are not from the same event
        event_ids = []
        for selection in selections:
            odds = Odds.objects.select_related('market__event').get(pk=selection.get('odds_id'))
            event_id = odds.market.event_id
            if event_id in event_ids:
                raise IncompatibleSelectionsException(
                    "Cannot combine multiple selections from the same event in a multi-bet"
                )
            event_ids.append(event_id)
        
        return True
    
    @staticmethod
    def validate_potential_winnings(stake, total_odds):
        """
        Validate potential winnings don't exceed maximum
        
        Args:
            stake (Decimal): Stake amount
            total_odds (Decimal): Total odds
            
        Raises:
            MaximumStakeExceededException: If potential winnings exceed maximum
        """
        potential_winnings = stake * total_odds
        if potential_winnings > MAX_POTENTIAL_WINNINGS:
            max_stake = MAX_POTENTIAL_WINNINGS / total_odds
            raise MaximumStakeExceededException(
                f"Maximum potential winnings exceeded. Maximum stake for these odds is {max_stake:.2f}"
            )
        
        return True


class BetService:
    """
    Service class for bet processing
    """
    
    def __init__(self):
        self.validator = BetValidator()
    
    def create_bet(self, user, stake, selections_data, bet_type='single', ip_address=None, device_info=None):
        """
        Create a new bet
        
        Args:
            user (CustomUser): User placing the bet
            stake (Decimal): Stake amount
            selections_data (list): List of selection data (odds_id, expected_value)
            bet_type (str): Type of bet ('single', 'multi', etc.)
            ip_address (str, optional): IP address of user
            device_info (str, optional): Device information
            
        Returns:
            Bet: Created bet object
            
        Raises:
            Various exceptions from BetValidator
        """
        # Convert stake to Decimal
        stake = Decimal(str(stake))
        
        # Validate bet parameters
        self.validator.validate_user_can_bet(user)
        self.validator.validate_stake(stake, bet_type)
        self.validator.validate_balance(user, stake)
        self.validator.validate_selections_count(len(selections_data))
        self.validator.validate_no_duplicate_selections(selections_data)
        self.validator.validate_compatible_selections(selections_data, bet_type)
        
        # Process selections and calculate total odds
        selections = []
        total_odds = Decimal('1.00')
        
        for selection_data in selections_data:
            odds_id = selection_data.get('odds_id')
            expected_value = selection_data.get('expected_value')
            
            odds = self.validator.validate_odds(odds_id, expected_value)
            self.validator.validate_event_not_started(odds.market.event)
            
            selections.append({
                'odds': odds,
                'market': odds.market,
                'selection': odds.selection,
                'odds_value': odds.odds_value
            })
            
            total_odds *= odds.odds_value
        
        # Validate potential winnings
        self.validator.validate_potential_winnings(stake, total_odds)
        
        # Create bet with transaction to ensure atomicity
        with transaction.atomic():
            # Deduct stake from user balance
            if not user.subtract_balance(stake):
                raise InsufficientBalanceException("Failed to deduct stake from balance")
            
            # Create bet
            bet = Bet.objects.create(
                user=user,
                bet_type=bet_type,
                stake=stake,
                total_odds=total_odds,
                potential_winnings=stake * total_odds,
                ip_address=ip_address,
                device_info=device_info,
                reference_code=self._generate_reference_code()
            )
            
            # Create selections
            for selection in selections:
                BetSelection.objects.create(
                    bet=bet,
                    market=selection['market'],
                    odds=selection['odds'],
                    selection=selection['selection'],
                    odds_value=selection['odds_value']
                )
            
            logger.info(f"Bet created: {bet} with {len(selections)} selections")
            
            return bet
    
    def cancel_bet(self, bet_id, user):
        """
        Cancel a bet
        
        Args:
            bet_id (int): ID of bet to cancel
            user (CustomUser): User requesting cancellation
            
        Returns:
            Bet: Cancelled bet object
            
        Raises:
            BetNotFoundException: If bet not found
            BetCancellationException: If bet cannot be cancelled
        """
        try:
            bet = Bet.objects.get(pk=bet_id, user=user)
        except Bet.DoesNotExist:
            raise BetNotFoundException("Bet not found")
        
        if not bet.can_be_cancelled():
            raise BetCancellationException("This bet cannot be cancelled")
        
        with transaction.atomic():
            # Refund stake to user balance
            bet.user.add_balance(bet.stake)
            
            # Mark bet as cancelled
            bet.mark_as_settled('cancelled')
            
            logger.info(f"Bet cancelled: {bet}")
            
            return bet
    
    def settle_bet(self, bet_id, admin_user=None):
        """
        Settle a bet based on selection results
        
        Args:
            bet_id (int): ID of bet to settle
            admin_user (CustomUser, optional): Admin user settling the bet
            
        Returns:
            Bet: Settled bet object
            
        Raises:
            BetNotFoundException: If bet not found
            BetAlreadySettledException: If bet already settled
        """
        try:
            bet = Bet.objects.select_related('user').prefetch_related('selections').get(pk=bet_id)
        except Bet.DoesNotExist:
            raise BetNotFoundException("Bet not found")
        
        if bet.is_settled:
            raise BetAlreadySettledException("Bet is already settled")
        
        # Check if all selections are settled
        all_settled = all(selection.is_settled for selection in bet.selections.all())
        if not all_settled:
            logger.warning(f"Cannot settle bet {bet_id}: not all selections are settled")
            return bet
        
        with transaction.atomic():
            # Determine bet status based on selections
            if bet.bet_type == 'single':
                # Single bet: status matches the selection status
                selection = bet.selections.first()
                bet_status = selection.status
            else:
                # Multi bet: won only if all selections won, void if all void, otherwise lost
                statuses = [selection.status for selection in bet.selections.all()]
                
                if all(status == 'won' for status in statuses):
                    bet_status = 'won'
                elif all(status == 'void' for status in statuses):
                    bet_status = 'void'
                elif any(status == 'void' for status in statuses):
                    # Recalculate odds excluding void selections
                    valid_selections = bet.selections.exclude(status='void')
                    if valid_selections.count() == 0:
                        bet_status = 'void'
                    else:
                        # Some selections void, others lost: bet is lost
                        if any(status == 'lost' for status in statuses):
                            bet_status = 'lost'
                        else:
                            bet_status = 'won'
                            # Recalculate odds for partial void
                            new_total_odds = Decimal('1.00')
                            for selection in valid_selections:
                                new_total_odds *= selection.odds_value
                            bet.total_odds = new_total_odds
                            bet.potential_winnings = bet.stake * new_total_odds
                            bet.save(update_fields=['total_odds', 'potential_winnings'])
                else:
                    bet_status = 'lost'
            
            # Mark bet as settled
            bet.mark_as_settled(bet_status)
            
            # If bet won, add winnings to user balance
            if bet_status == 'won':
                bet.user.add_balance(bet.potential_winnings)
                logger.info(f"Bet {bet_id} won: {bet.potential_winnings} added to user balance")
            
            # If bet void, refund stake
            elif bet_status == 'void':
                bet.user.add_balance(bet.stake)
                logger.info(f"Bet {bet_id} void: {bet.stake} refunded to user balance")
            
            logger.info(f"Bet settled: {bet} with status {bet_status}")
            
            return bet
    
    def load_bet_by_reference(self, reference_code):
        """
        Load a bet by reference code
        
        Args:
            reference_code (str): Bet reference code
            
        Returns:
            Bet: Found bet object
            
        Raises:
            BetNotFoundException: If bet not found
        """
        try:
            return Bet.objects.get(reference_code=reference_code)
        except Bet.DoesNotExist:
            raise BetNotFoundException(f"No bet found with reference code {reference_code}")
    
    def _generate_reference_code(self):
        """Generate unique reference code for bet"""
        return uuid.uuid4().hex[:8].upper()


class MultiBetService:
    """Service for managing multi-bet functionality"""

    def __init__(self):
        self.betting_service = BetService()

    def place_multi_bet(self, user: CustomUser, selections: list, stake: Decimal, bet_type: str = 'multi') -> Bet:
        """
        Place a multi-bet with multiple selections

        Args:
            user: User placing the bet
            selections: List of selection dictionaries
            stake: Total stake amount
            bet_type: Type of multi-bet ('multi', 'accumulator', 'system')

        Returns:
            Bet: Created multi-bet instance
        """
        try:
            with transaction.atomic():
                # Validate multi-bet requirements
                self._validate_multi_bet(selections, stake, bet_type)

                # Check for duplicate or incompatible selections
                self._validate_selections_compatibility(selections)

                # Calculate combined odds
                combined_odds = self._calculate_combined_odds(selections, bet_type)

                # Create the bet using existing service
                bet = self.betting_service.place_bet(
                    user=user,
                    selections=selections,
                    stake=stake,
                    bet_type=bet_type
                )

                # Update with multi-bet specific data
                bet.total_odds = combined_odds
                bet.potential_winnings = stake * combined_odds
                bet.selections_count = len(selections)
                bet.save(update_fields=['total_odds', 'potential_winnings', 'selections_count'])

                logger.info(f"Multi-bet placed: {bet.id} with {len(selections)} selections")
                return bet

        except Exception as e:
            logger.error(f"Error placing multi-bet: {e}")
            raise

    def _validate_multi_bet(self, selections: list, stake: Decimal, bet_type: str):
        """Validate multi-bet requirements"""
        if len(selections) < 2:
            raise InvalidSelectionException("Multi-bet requires at least 2 selections")

        if len(selections) > 20:  # Maximum selections limit
            raise MaximumSelectionsExceededException("Maximum 20 selections allowed")

        if bet_type == 'system':
            # System bets have additional requirements
            if len(selections) < 3:
                raise InvalidSelectionException("System bet requires at least 3 selections")

    def _validate_selections_compatibility(self, selections: list):
        """Check for duplicate or incompatible selections"""
        seen_markets = set()
        seen_events = set()

        for selection in selections:
            market_id = selection['market_id']

            # Check for duplicate markets
            if market_id in seen_markets:
                raise DuplicateSelectionException("Cannot select multiple outcomes from the same market")
            seen_markets.add(market_id)

            # Get market and event info
            try:
                market = Market.objects.get(id=market_id)
                event_id = str(market.event.id)

                # For some bet types, check event compatibility
                if event_id in seen_events:
                    # Allow multiple markets from same event for certain bet types
                    if not self._are_markets_compatible(seen_markets, market):
                        raise IncompatibleSelectionsException("Selected markets are not compatible")

                seen_events.add(event_id)

            except Market.DoesNotExist:
                raise InvalidSelectionException(f"Market {market_id} not found")

    def _are_markets_compatible(self, existing_markets: set, new_market: Market) -> bool:
        """Check if markets from the same event are compatible"""
        # This is a simplified check - in practice, you'd have complex rules
        # For example, "Match Winner" and "Over/Under" might be compatible
        # but "Home Win" and "Away Win" would not be

        # For now, allow all combinations
        return True

    def _calculate_combined_odds(self, selections: list, bet_type: str) -> Decimal:
        """Calculate combined odds for multi-bet"""
        if bet_type in ['multi', 'accumulator']:
            return self._calculate_accumulator_odds(selections)
        elif bet_type == 'system':
            return self._calculate_system_odds(selections)
        else:
            return Decimal('1.00')

    def _calculate_accumulator_odds(self, selections: list) -> Decimal:
        """Calculate accumulator odds (multiply all odds)"""
        total_odds = Decimal('1.00')

        for selection in selections:
            try:
                odds = Odds.objects.get(id=selection['odds_id'])
                total_odds *= odds.value
            except Odds.DoesNotExist:
                raise InvalidOddsException(f"Odds {selection['odds_id']} not found")

        return total_odds

    def _calculate_system_odds(self, selections: list, system_type: str = "2/3") -> Decimal:
        """Calculate system bet odds"""
        # This is a simplified system calculation
        # Real system bets involve complex combinatorial mathematics

        try:
            min_wins, total_selections = map(int, system_type.split('/'))

            if total_selections != len(selections):
                raise InvalidSelectionException(f"System type {system_type} doesn't match {len(selections)} selections")

            # Calculate base accumulator odds
            base_odds = self._calculate_accumulator_odds(selections)

            # Apply system reduction factor
            reduction_factor = Decimal(str(min_wins)) / Decimal(str(total_selections))

            return base_odds * reduction_factor

        except Exception as e:
            logger.error(f"Error calculating system odds: {e}")
            return Decimal('1.00')

    def settle_multi_bet(self, bet: Bet) -> dict:
        """
        Settle a multi-bet based on selection results

        Args:
            bet: Multi-bet to settle

        Returns:
            dict: Settlement result
        """
        try:
            with transaction.atomic():
                if bet.status != 'pending':
                    return {'error': 'Bet already settled'}

                selections = bet.selections.all()
                won_count = selections.filter(status='won').count()
                lost_count = selections.filter(status='lost').count()
                void_count = selections.filter(status='void').count()
                total_count = selections.count()

                # Update bet statistics
                bet.winning_selections = won_count
                bet.void_selections = void_count

                # Determine settlement based on bet type
                if bet.bet_type in ['multi', 'accumulator']:
                    result = self._settle_accumulator(bet, won_count, lost_count, void_count, total_count)
                elif bet.bet_type == 'system':
                    result = self._settle_system_bet(bet, won_count, total_count)
                else:
                    result = {'status': 'lost', 'winnings': Decimal('0.00')}

                # Update bet with result
                bet.status = result['status']
                bet.actual_winnings = result['winnings']
                bet.settled_at = timezone.now()
                bet.save()

                # Credit winnings if any
                if result['winnings'] > 0:
                    self._credit_winnings(bet.user, result['winnings'], bet)

                logger.info(f"Multi-bet settled: {bet.id} - {result['status']} - KES {result['winnings']}")
                return result

        except Exception as e:
            logger.error(f"Error settling multi-bet: {e}")
            return {'error': str(e)}

    def _settle_accumulator(self, bet: Bet, won_count: int, lost_count: int, void_count: int, total_count: int) -> dict:
        """Settle accumulator/multi bet"""
        if lost_count > 0:
            # Any lost selection means entire bet is lost
            return {'status': 'lost', 'winnings': Decimal('0.00')}

        if void_count == total_count:
            # All selections void - return stake
            return {'status': 'void', 'winnings': bet.stake}

        if won_count + void_count == total_count:
            # All selections won or void
            if void_count > 0:
                # Recalculate odds excluding void selections
                active_odds = Decimal('1.00')
                for selection in bet.selections.filter(status='won'):
                    active_odds *= selection.odds_value
                winnings = bet.stake * active_odds
                return {'status': 'partially_won', 'winnings': winnings}
            else:
                # All won - full payout
                return {'status': 'won', 'winnings': bet.potential_winnings}

        return {'status': 'pending', 'winnings': Decimal('0.00')}

    def _settle_system_bet(self, bet: Bet, won_count: int, total_count: int) -> dict:
        """Settle system bet"""
        if not bet.system_type:
            return {'status': 'lost', 'winnings': Decimal('0.00')}

        try:
            min_wins, _ = map(int, bet.system_type.split('/'))

            if won_count >= min_wins:
                # Calculate proportional winnings
                win_ratio = Decimal(str(won_count)) / Decimal(str(total_count))
                winnings = bet.potential_winnings * win_ratio

                if won_count == total_count:
                    return {'status': 'won', 'winnings': winnings}
                else:
                    return {'status': 'partially_won', 'winnings': winnings}
            else:
                return {'status': 'lost', 'winnings': Decimal('0.00')}

        except Exception as e:
            logger.error(f"Error settling system bet: {e}")
            return {'status': 'lost', 'winnings': Decimal('0.00')}

    def _credit_winnings(self, user: CustomUser, amount: Decimal, bet: Bet):
        """Credit winnings to user account"""
        try:
            user.balance += amount
            user.save(update_fields=['balance'])

            # Create transaction record
            from payments.models import Transaction
            Transaction.objects.create(
                user=user,
                transaction_type='credit',
                amount=amount,
                status='completed',
                description=f'Multi-bet winnings - {bet.get_bet_type_display()}',
                metadata={
                    'bet_id': str(bet.id),
                    'bet_type': bet.bet_type,
                    'selections_count': bet.selections_count,
                    'original_stake': str(bet.stake)
                }
            )

            logger.info(f"Credited {amount} to user {user.id} for multi-bet {bet.id}")

        except Exception as e:
            logger.error(f"Error crediting multi-bet winnings: {e}")

    def void_selection_in_multi_bet(self, selection_id: str, reason: str = '') -> dict:
        """
        Void a selection in a multi-bet and recalculate

        Args:
            selection_id: ID of selection to void
            reason: Reason for voiding

        Returns:
            dict: Void result
        """
        try:
            with transaction.atomic():
                selection = BetSelection.objects.get(id=selection_id, status='pending')
                bet = selection.bet

                # Void the selection
                selection.status = 'void'
                selection.settled_at = timezone.now()
                selection.metadata['void_reason'] = reason
                selection.save()

                # Update bet void count
                bet.void_selections += 1

                # Recalculate odds for multi-bet
                if bet.is_multi_bet:
                    new_odds = self._recalculate_odds_after_void(bet)
                    bet.total_odds = new_odds
                    bet.potential_winnings = bet.stake * new_odds

                bet.save()

                logger.info(f"Selection {selection_id} voided in multi-bet {bet.id}: {reason}")
                return {'success': True, 'new_odds': str(new_odds), 'new_potential_winnings': str(bet.potential_winnings)}

        except BetSelection.DoesNotExist:
            return {'error': 'Selection not found or already settled'}
        except Exception as e:
            logger.error(f"Error voiding selection in multi-bet: {e}")
            return {'error': str(e)}
</content>