"""
Betting models for single bets, multi-bets, and system bets
"""

import uuid
from decimal import Decimal
from typing import TYPE_CHECKING
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.validators import MinValueValidator
from sports.models import Market, Odds

if TYPE_CHECKING:
    pass

User = get_user_model()


class Bet(models.Model):
    """
    Represents a bet placed by a user
    """
    BET_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('won', 'Won'),
        ('lost', 'Lost'),
        ('void', 'Void'),
        ('partially_won', 'Partially Won'),
        ('partially_void', 'Partially Void'),
    ]

    BET_TYPE_CHOICES = [
        ('single', 'Single Bet'),
        ('accumulator', 'Accumulator'),
        ('system', 'System Bet'),
        ('multi', 'Multi Bet'),
        ('combo', 'Combo Bet'),
    ]

    if TYPE_CHECKING:
        # Type hint for reverse relationship
        from django.db.models.manager import RelatedManager
        selections: 'RelatedManager[BetSelection]'

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='bets')

    # Bet details
    bet_type = models.CharField(max_length=20, choices=BET_TYPE_CHOICES, default='single')
    stake = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('1.00'))]
    )

    # Odds and winnings
    total_odds = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('1.00'),
        validators=[MinValueValidator(Decimal('1.00'))]
    )
    potential_winnings = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))]
    )
    actual_winnings = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))]
    )

    # Multi-bet specific fields
    selections_count = models.PositiveIntegerField(default=1)
    winning_selections = models.PositiveIntegerField(default=0)
    void_selections = models.PositiveIntegerField(default=0)

    # System bet fields
    system_type = models.CharField(max_length=20, blank=True)  # e.g., "2/3", "3/5"
    system_combinations = models.PositiveIntegerField(default=0)

    # Status and timing
    status = models.CharField(max_length=20, choices=BET_STATUS_CHOICES, default='pending')
    placed_at = models.DateTimeField(auto_now_add=True)
    settled_at = models.DateTimeField(null=True, blank=True)

    # Metadata
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        ordering = ['-placed_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['bet_type', 'status']),
            models.Index(fields=['placed_at']),
        ]

    def __str__(self):
        if self.user:
            user_identifier = getattr(self.user, 'phone_number', f'User {getattr(self.user, "id", "Unknown")}')
        else:
            user_identifier = 'Unknown User'
        bet_type_display = getattr(self, 'get_bet_type_display', lambda: self.bet_type)()
        return f"{user_identifier} - {bet_type_display} - KES {self.stake}"

    @property
    def is_multi_bet(self):
        """Check if this is a multi-bet (more than one selection)"""
        return self.bet_type in ['accumulator', 'multi', 'combo', 'system'] or self.selections_count > 1

    @property
    def is_system_bet(self):
        """Check if this is a system bet"""
        return self.bet_type == 'system'

    def calculate_total_odds(self):
        """Calculate total odds based on selections"""
        if self.bet_type == 'single':
            selection = self.selections.first()
            return selection.odds_value if selection else Decimal('1.00')

        elif self.bet_type in ['accumulator', 'multi', 'combo']:
            # Multiply all odds together
            total = Decimal('1.00')
            for selection in self.selections.filter(status__in=['pending', 'won']):
                total *= selection.odds_value
            return total

        elif self.bet_type == 'system':
            # System bet calculation is more complex
            return self._calculate_system_odds()

        return Decimal('1.00')

    def _calculate_system_odds(self):
        """Calculate odds for system bet"""
        # This is a simplified system bet calculation
        # In practice, this would be much more complex
        active_selections = self.selections.filter(status__in=['pending', 'won'])
        if active_selections.count() < 2:
            return Decimal('1.00')

        # For now, use accumulator calculation
        total = Decimal('1.00')
        for selection in active_selections:
            total *= selection.odds_value
        return total

    def calculate_potential_winnings(self):
        """Calculate potential winnings"""
        return self.stake * self.total_odds

    def update_odds_and_winnings(self):
        """Update total odds and potential winnings"""
        self.total_odds = self.calculate_total_odds()
        self.potential_winnings = self.calculate_potential_winnings()
        self.save(update_fields=['total_odds', 'potential_winnings'])


class BetSelection(models.Model):
    """
    Individual selection within a bet
    """
    SELECTION_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('won', 'Won'),
        ('lost', 'Lost'),
        ('void', 'Void'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    bet = models.ForeignKey(Bet, on_delete=models.CASCADE, related_name='selections')

    # Selection details
    market = models.ForeignKey(Market, on_delete=models.CASCADE)
    odds = models.ForeignKey(Odds, on_delete=models.CASCADE)
    odds_value = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('1.00'))]
    )

    # Status
    status = models.CharField(max_length=20, choices=SELECTION_STATUS_CHOICES, default='pending')
    settled_at = models.DateTimeField(null=True, blank=True)

    # Metadata
    metadata = models.JSONField(default=dict, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['bet', 'status']),
            models.Index(fields=['market', 'status']),
        ]

    def __str__(self):
        return f"{self.bet.id} - {self.odds.selection} @ {self.odds_value}"

    def settle(self, result_status):
        """Settle this selection with the given result"""
        self.status = result_status
        self.settled_at = timezone.now()
        self.save(update_fields=['status', 'settled_at'])

        # Update bet statistics
        self.bet.refresh_from_db()
        if result_status == 'won':
            self.bet.winning_selections += 1
        elif result_status == 'void':
            self.bet.void_selections += 1

        self.bet.save(update_fields=['winning_selections', 'void_selections'])

        # Check if bet should be settled
        self._check_bet_settlement()

    def _check_bet_settlement(self):
        """Check if the parent bet should be settled"""
        bet = self.bet
        total_selections = bet.selections.count()
        settled_selections = bet.selections.exclude(status='pending').count()

        # If all selections are settled, settle the bet
        if settled_selections == total_selections:
            from .services import BetService
            betting_service = BetService()
            betting_service.settle_bet(bet.id)
